package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.payne.server.banknote.entity.LabelTemplate;
import com.payne.server.banknote.entity.PjOSendform;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.service.BatchPrintService;
import com.payne.server.banknote.service.LabelDesignService;
import com.payne.server.banknote.service.PjOSendformItemService;
import com.payne.server.banknote.service.PjOSendformService;
import com.payne.server.banknote.vo.BatchPrintCoinVO;
import com.payne.server.banknote.vo.PrintAuditResultVO;
import com.payne.server.banknote.vo.PrintDataVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量打印服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchPrintServiceImpl implements BatchPrintService {

    private final PjOSendformItemService pjOSendformItemService;
    private final PjOSendformService pjOSendformService;
    private final LabelDesignService labelDesignService;

    @Value("${app.preview.base-url:http://localhost:8080}")
    private String baseUrl;

    @Override
    public IPage<BatchPrintCoinVO> convertToVOPage(IPage<PjOSendformItem> page) {
        IPage<BatchPrintCoinVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        
        List<BatchPrintCoinVO> voList = page.getRecords().stream().map(item -> {
            BatchPrintCoinVO vo = new BatchPrintCoinVO();
            BeanUtils.copyProperties(item, vo);

            // 直接设置ID字段（String类型）
            vo.setId(item.getId());

            // 设置审核状态（暂时设为已审核，实际需要根据业务逻辑设置）
            vo.setAuditStatus(1);

            // 设置送评单号
            vo.setSendformNumber(item.getSendnum());
            
            // 组合钱币名称
            StringBuilder coinName = new StringBuilder();
            if (StringUtils.hasText(item.getCoinName1())) {
                coinName.append(item.getCoinName1());
            }
            if (StringUtils.hasText(item.getCoinName2())) {
                if (coinName.length() > 0) coinName.append(" ");
                coinName.append(item.getCoinName2());
            }
            if (StringUtils.hasText(item.getCoinName3())) {
                if (coinName.length() > 0) coinName.append(" ");
                coinName.append(item.getCoinName3());
            }
            vo.setCoinName(coinName.toString());
            
            // 设置其他字段
            vo.setVersion(item.getVersion());
            vo.setAdditionalInfo(item.getSpecialLabel());
            vo.setYearInfo(item.getYearInfo());
            vo.setGradeLevel(item.getRank());
            vo.setWeight(item.getCoinWeight() != null ? item.getCoinWeight().toString() : "");
            vo.setSize(item.getCoinSize());
            // 将String类型的gradeScore转换为Integer
            try {
                if (item.getGradeScore() != null && !item.getGradeScore().trim().isEmpty()) {
                    // 尝试从字符串中提取数字
                    String scoreStr = item.getGradeScore().replaceAll("[^0-9]", "");
                    if (!scoreStr.isEmpty()) {
                        vo.setGradeScore(Integer.parseInt(scoreStr));
                    }
                }
            } catch (NumberFormatException e) {
                vo.setGradeScore(null);
            }
            // 通过送评单号查询客户姓名
            if (item.getSendnum() != null) {
                LambdaQueryWrapper<PjOSendform> sendformWrapper = new LambdaQueryWrapper<>();
                sendformWrapper.eq(PjOSendform::getSendnum, item.getSendnum());
                PjOSendform sendform = pjOSendformService.getOne(sendformWrapper);
                if (sendform != null) {
                    vo.setCustomerName(sendform.getRname() != null ? sendform.getRname() : "");
                } else {
                    vo.setCustomerName("");
                }
            } else {
                vo.setCustomerName("");
            }
            vo.setFee(item.getGradeFee());
            vo.setCoinType(item.getCoinType());
            vo.setBankName(item.getBankName());

            return vo;
        }).collect(Collectors.toList());
        
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public PrintAuditResultVO checkAuditStatus(List<String> coinIds) {
        PrintAuditResultVO result = new PrintAuditResultVO();
        
        // 查询钱币信息
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        List<String> validIds = coinIds.stream()
                .filter(Objects::nonNull)
                .filter(id -> !id.trim().isEmpty())
                .collect(Collectors.toList());

        if (validIds.isEmpty()) {
            result.setTotalCount(0);
            result.setUnauditedCount(0);
            result.setUnauditedSendforms(new ArrayList<>());
            return result;
        }

        wrapper.in(PjOSendformItem::getId, validIds);
        List<PjOSendformItem> coins = pjOSendformItemService.list(wrapper);
        
        result.setTotalCount(coins.size());
        
        // 通过送评单号查询审核状态
        Set<String> sendnums = coins.stream()
                .map(PjOSendformItem::getSendnum)
                .filter(StringUtils::hasText)
                .collect(Collectors.toSet());

        List<String> unauditedSendforms = new ArrayList<>();
        int unauditedCount = 0;

        if (!sendnums.isEmpty()) {
            LambdaQueryWrapper<PjOSendform> sendformWrapper = new LambdaQueryWrapper<>();
            sendformWrapper.in(PjOSendform::getSendnum, sendnums);
            List<PjOSendform> sendforms = pjOSendformService.list(sendformWrapper);

            for (PjOSendform sendform : sendforms) {
                if (sendform.getCheckStatus() == null || sendform.getCheckStatus() != 1) {
                    unauditedSendforms.add(sendform.getSendnum());
                    // 统计该送评单下的钱币数量
                    long coinCount = coins.stream()
                            .filter(coin -> sendform.getSendnum().equals(coin.getSendnum()))
                            .count();
                    unauditedCount += coinCount;
                }
            }
        }

        result.setUnauditedCount(unauditedCount);
        result.setAuditedCount(coins.size() - unauditedCount);
        result.setUnauditedSendforms(unauditedSendforms);
        
        if (result.getUnauditedSendforms() == null) {
            result.setUnauditedSendforms(new ArrayList<>());
        }
        
        return result;
    }

    @Override
    public PrintDataVO generateCustomTemplatePrintData(List<String> coinIds, String templateId,
                                                      Integer conversionType, String printType) {
        // 获取自定义模板
        LabelTemplate template = labelDesignService.getById(templateId);
        if (template == null) {
            throw new RuntimeException("模板不存在: " + templateId);
        }

        // 解析模板配置
        Map<String, Object> layoutConfig = parseLayoutConfig(template.getLayoutConfig());
        Map<String, List<String>> fieldMapping = parseFieldMapping(template.getFieldMapping());

        // 查询钱币数据
        List<PjOSendformItem> coins = getCoinsByIds(coinIds);

        // 转换为自定义模板数据
        PrintDataVO printData = new PrintDataVO();
        printData.setItems(convertToCustomTemplateData(coins, fieldMapping));
        printData.setTotalCount(coins.size());
        printData.setTemplateId(templateId);
        printData.setTemplateName(template.getTemplateName());
        printData.setLayoutConfig(layoutConfig); // 直接使用原始模板，不进行字段替换
        printData.setFieldMapping(fieldMapping);
        printData.setCoinIds(coinIds);
        printData.setConversionType(conversionType);
        printData.setPrintType(printType);

        // 添加纸张设置信息
        if (template.getPageSettings() != null) {
            printData.setPageSettings(template.getPageSettings());
        }

        return printData;
    }

    private Map<String, Object> parseLayoutConfig(String layoutConfigJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(layoutConfigJson, Map.class);
        } catch (Exception e) {
            log.warn("解析布局配置失败: {}", e.getMessage());
            return getDefaultLayoutConfig();
        }
    }

    private Map<String, List<String>> parseFieldMapping(String fieldMappingJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            TypeReference<Map<String, List<String>>> typeRef = new TypeReference<Map<String, List<String>>>() {};
            return mapper.readValue(fieldMappingJson, typeRef);
        } catch (Exception e) {
            log.warn("解析字段映射失败: {}", e.getMessage());
            return getDefaultFieldMapping();
        }
    }

    private List<PjOSendformItem> getCoinsByIds(List<String> coinIds) {
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        List<String> validIds = coinIds.stream()
                .filter(Objects::nonNull)
                .filter(id -> !id.trim().isEmpty())
                .collect(Collectors.toList());

        if (validIds.isEmpty()) {
            return new ArrayList<>();
        }

        wrapper.in(PjOSendformItem::getId, validIds);
        return pjOSendformItemService.list(wrapper);
    }

    private List<BatchPrintCoinVO> convertToCustomTemplateData(List<PjOSendformItem> coins,
                                                              Map<String, List<String>> fieldMapping) {
        return coins.stream().map(coin -> {
            BatchPrintCoinVO vo = convertToVOBasic(coin);

            // 为 hiprint 添加所有可能用到的字段，让 hiprint 根据模板的 field 属性自动绑定
            vo.setSerialNumber(coin.getSerialNumber());
            vo.setVersion(coin.getVersion());
            vo.setBankName(coin.getBankName());
            vo.setCoinName(getCombinedCoinName(coin));
            vo.setCoinName1(coin.getCoinName1());
            vo.setCoinName2(coin.getCoinName2());
            vo.setCoinName3(coin.getCoinName3());
            vo.setYearInfo(coin.getYearInfo());
            vo.setGradeLevel(coin.getRank());
            vo.setSpecialMark(coin.getSpecialMark());
            vo.setAuthenticity(coin.getAuthenticity());
            vo.setDiyCode(coin.getDiyCode());
            vo.setCustomerName(getCustomerName(coin));
            vo.setWeight(coin.getCoinWeight() != null ? coin.getCoinWeight().toString() : "");
            vo.setSize(coin.getCoinSize());
            vo.setCoinType(coin.getCoinType());

            // 添加组合字段，供 hiprint 使用
            vo.setSerialNumberWithVersion(formatSerialNumberWithVersion(coin));
            vo.setQrcode(generatePreviewUrl(coin.getDiyCode())); // 二维码URL（供扫码访问预览页面）
            vo.setQrCodeContent(coin.getDiyCode()); // 原始送评条码（供显示）

            // 根据字段映射添加自定义字段数据（保持向后兼容）
            Map<String, Object> customFields = new HashMap<>();
            fieldMapping.forEach((zoneId, fields) -> {
                Map<String, Object> zoneData = new HashMap<>();
                fields.forEach(fieldName -> {
                    Object fieldValue = getFieldValue(coin, fieldName);
                    zoneData.put(fieldName, fieldValue);
                });
                customFields.put(zoneId, zoneData);
            });

            vo.setCustomFields(customFields);
            return vo;
        }).collect(Collectors.toList());
    }


    private Object getFieldValue(PjOSendformItem coin, String fieldName) {
        switch (fieldName) {
            case "bankName": return coin.getBankName();
            case "coinName": return getCombinedCoinName(coin);
            case "coinName1": return coin.getCoinName1();
            case "coinName2": return coin.getCoinName2();
            case "coinName3": return coin.getCoinName3();
            case "yearInfo": return coin.getYearInfo();
            case "serialNumber": return coin.getSerialNumber();
            case "version": return coin.getVersion();
            case "serialNumberWithVersion": return formatSerialNumberWithVersion(coin);
            case "gradeScore": return coin.getGradeScore();
            case "gradeLevel": return coin.getRank(); // 使用rank字段作为gradeLevel
            case "specialMark": return coin.getSpecialMark();
            case "authenticity": return coin.getAuthenticity();
            case "diyCode": return coin.getDiyCode();
            case "qrcode": return generatePreviewUrl(coin.getDiyCode()); // 二维码URL
            case "qrCodeContent": return coin.getDiyCode(); // 原始送评条码
            case "customerName": return getCustomerName(coin);
            case "weight": return coin.getCoinWeight() != null ? coin.getCoinWeight().toString() : "";
            case "size": return coin.getCoinSize();
            case "coinType": return coin.getCoinType();
            default: return "";
        }
    }

    /**
     * 获取组合的钱币名称
     */
    private String getCombinedCoinName(PjOSendformItem coin) {
        StringBuilder coinName = new StringBuilder();
        if (StringUtils.hasText(coin.getCoinName1())) {
            coinName.append(coin.getCoinName1());
        }
        if (StringUtils.hasText(coin.getCoinName2())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(coin.getCoinName2());
        }
        if (StringUtils.hasText(coin.getCoinName3())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(coin.getCoinName3());
        }
        return coinName.toString();
    }

    /**
     * 获取客户姓名
     */
    private String getCustomerName(PjOSendformItem coin) {
        if (coin.getSendnum() != null) {
            LambdaQueryWrapper<PjOSendform> sendformWrapper = new LambdaQueryWrapper<>();
            sendformWrapper.eq(PjOSendform::getSendnum, coin.getSendnum());
            PjOSendform sendform = pjOSendformService.getOne(sendformWrapper);
            if (sendform != null) {
                return sendform.getRname() != null ? sendform.getRname() : "";
            }
        }
        return "";
    }

    /**
     * 格式化编号-版别组合字段
     */
    private String formatSerialNumberWithVersion(PjOSendformItem coin) {
        String serialNumber = coin.getSerialNumber();
        String version = coin.getVersion();

        if (serialNumber != null && !serialNumber.trim().isEmpty() &&
            version != null && !version.trim().isEmpty()) {
            return serialNumber + " - " + version;
        } else if (serialNumber != null && !serialNumber.trim().isEmpty()) {
            return serialNumber;
        } else if (version != null && !version.trim().isEmpty()) {
            return version;
        }
        return "";
    }

    /**
     * 生成送评单预览页面URL
     * @param diyCode 送评条码
     * @return 完整的预览页面URL
     */
    private String generatePreviewUrl(String diyCode) {
        if (diyCode == null || diyCode.trim().isEmpty()) {
            return "";
        }

        // 使用配置文件中的基础URL
        String previewPath = "/api/pjosendform/preview/";
        String fullUrl = baseUrl + previewPath + diyCode.trim();

        log.debug("生成预览URL: baseUrl={}, diyCode={}, fullUrl={}", baseUrl, diyCode, fullUrl);

        return fullUrl;
    }

    private Map<String, Object> getDefaultLayoutConfig() {
        Map<String, Object> config = new HashMap<>();
        Map<String, Object> canvas = new HashMap<>();
        canvas.put("width", 200);
        canvas.put("height", 25);
        config.put("canvas", canvas);

        List<Map<String, Object>> zones = new ArrayList<>();
        // 公司Logo区域
        Map<String, Object> logoZone = new HashMap<>();
        logoZone.put("id", "logo");
        logoZone.put("name", "公司Logo");
        logoZone.put("x", 0);
        logoZone.put("y", 0);
        logoZone.put("width", 30);
        logoZone.put("height", 25);
        zones.add(logoZone);

        // 钱币信息区域
        Map<String, Object> coinZone = new HashMap<>();
        coinZone.put("id", "coinInfo");
        coinZone.put("name", "钱币信息");
        coinZone.put("x", 30);
        coinZone.put("y", 0);
        coinZone.put("width", 100);
        coinZone.put("height", 25);
        zones.add(coinZone);

        // 评级信息区域
        Map<String, Object> gradeZone = new HashMap<>();
        gradeZone.put("id", "gradeInfo");
        gradeZone.put("name", "评级信息");
        gradeZone.put("x", 130);
        gradeZone.put("y", 0);
        gradeZone.put("width", 70);
        gradeZone.put("height", 25);
        zones.add(gradeZone);

        config.put("zones", zones);
        return config;
    }

    private Map<String, List<String>> getDefaultFieldMapping() {
        Map<String, List<String>> mapping = new HashMap<>();
        mapping.put("coinInfo", Arrays.asList("bankName", "coinName1", "serialNumber"));
        mapping.put("gradeInfo", Arrays.asList("gradeScore", "gradeLevel"));
        return mapping;
    }



    @Override
    public Map<String, Object> generatePreviewData(List<String> coinIds, String templateId, Integer conversionType) {
        Map<String, Object> previewData = new HashMap<>();

        PrintDataVO printData = generateCustomTemplatePrintData(coinIds, templateId, conversionType, null);
        previewData.put("printData", printData);

        // 添加预览配置信息
        previewData.put("previewUrl", "/api/batch-print/preview-report");
        previewData.put("totalCount", coinIds.size());
        previewData.put("templateId", templateId);
        previewData.put("conversionType", conversionType);

        return previewData;
    }

    @Override
    public void executePrint(List<String> coinIds, String templateId, Integer conversionType, String action, HttpServletResponse response) {
        try {
            // 生成打印数据
            PrintDataVO printData = generateCustomTemplatePrintData(coinIds, templateId, conversionType, action);

            // 设置响应头
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition",
                    "attachment; filename=batch_print_" + System.currentTimeMillis() + ".pdf");

            // 这里应该调用报表生成服务生成PDF
            // 目前先返回一个模拟的PDF内容
            String pdfContent = generateMockPDF(printData);

            OutputStream outputStream = response.getOutputStream();
            outputStream.write(pdfContent.getBytes());
            outputStream.flush();
            outputStream.close();

        } catch (IOException e) {
            throw new RuntimeException("生成打印文件失败", e);
        }
    }

    /**
     * 生成模拟PDF内容
     */
    private String generateMockPDF(PrintDataVO printData) {
        StringBuilder content = new StringBuilder();
        content.append("批量打印标签\n");
        content.append("模板名称：").append(printData.getTemplateName()).append("\n");
        content.append("转换类型：").append(printData.getConversionTypeName()).append("\n");
        content.append("打印数量：").append(printData.getTotalCount()).append("\n\n");

        for (BatchPrintCoinVO coin : printData.getItems()) {
            content.append("钱币编号：").append(coin.getSerialNumber()).append("\n");
            content.append("钱币名称：").append(coin.getCoinName()).append("\n");
            content.append("评级分数：").append(coin.getGradeScore()).append("\n");
            content.append("客户姓名：").append(coin.getCustomerName()).append("\n");
            content.append("---\n");
        }

        return content.toString();
    }



    /**
     * 转换为基础VO对象
     */
    private BatchPrintCoinVO convertToVOBasic(PjOSendformItem item) {
        BatchPrintCoinVO vo = new BatchPrintCoinVO();
        BeanUtils.copyProperties(item, vo);

        vo.setId(item.getId());
        vo.setAuditStatus(1);
        vo.setSendformNumber(item.getSendnum());

        // 组合钱币名称
        StringBuilder coinName = new StringBuilder();
        if (StringUtils.hasText(item.getCoinName1())) {
            coinName.append(item.getCoinName1());
        }
        if (StringUtils.hasText(item.getCoinName2())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(item.getCoinName2());
        }
        if (StringUtils.hasText(item.getCoinName3())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(item.getCoinName3());
        }
        vo.setCoinName(coinName.toString());

        // 设置其他字段
        vo.setVersion(item.getVersion());
        vo.setAdditionalInfo(item.getSpecialLabel());
        vo.setYearInfo(item.getYearInfo());
        vo.setGradeLevel(item.getRank());
        vo.setWeight(item.getCoinWeight() != null ? item.getCoinWeight().toString() : "");
        vo.setSize(item.getCoinSize());

        // 转换评级分数
        try {
            if (item.getGradeScore() != null && !item.getGradeScore().trim().isEmpty()) {
                String scoreStr = item.getGradeScore().replaceAll("[^0-9]", "");
                if (!scoreStr.isEmpty()) {
                    vo.setGradeScore(Integer.parseInt(scoreStr));
                }
            }
        } catch (NumberFormatException e) {
            vo.setGradeScore(null);
        }

        // 查询客户姓名
        if (item.getSendnum() != null) {
            LambdaQueryWrapper<PjOSendform> sendformWrapper = new LambdaQueryWrapper<>();
            sendformWrapper.eq(PjOSendform::getSendnum, item.getSendnum());
            PjOSendform sendform = pjOSendformService.getOne(sendformWrapper);
            if (sendform != null) {
                vo.setCustomerName(sendform.getRname() != null ? sendform.getRname() : "");
            } else {
                vo.setCustomerName("");
            }
        } else {
            vo.setCustomerName("");
        }

        vo.setFee(item.getGradeFee());
        vo.setCoinType(item.getCoinType());
        vo.setBankName(item.getBankName());

        return vo;
    }


}
