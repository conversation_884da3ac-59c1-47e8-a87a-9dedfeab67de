package com.payne.server.banknote.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.payne.core.annotation.OperationLog;
import com.payne.core.constant.Constants;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.server.banknote.dto.CoinScoreCheckResult;
import com.payne.server.banknote.dto.SendformCheckDto;
import com.payne.server.banknote.dto.SendformCreateDto;
import com.payne.server.banknote.dto.SendformDetailDto;
import com.payne.server.banknote.entity.PjOSendform;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.enums.CheckStatusEnum;
import com.payne.server.banknote.enums.CoinTypeEnum;
import com.payne.server.banknote.util.CodeCommonUtil;
import com.payne.server.banknote.mapper.PjOSendformMapper;
import com.payne.server.banknote.param.PjOSendformParam;
import com.payne.server.banknote.service.PjOSendformItemService;
import com.payne.server.banknote.service.PjOSendformService;
import com.payne.upms.system.entity.SysAccount;
import com.payne.upms.system.entity.SysRole;
import com.payne.upms.utils.SecurityUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 送评单控制器
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/pjosendform")
public class PjOSendformController extends BaseController {
    
    @Resource
    private PjOSendformService pjOSendformService;

    @Resource
    private PjOSendformItemService pjOSendformItemService;

    @Resource
    private PjOSendformMapper pjOSendformMapper;

    /**
     * 分页查询送评单
     */
    @PreAuthorize("hasAuthority('banknote:sendform:list')")
    @OperationLog
    @GetMapping("/page")
    public ApiResult<PageResult<PjOSendform>> page(PjOSendformParam param) {
        PageParam<PjOSendform, PjOSendformParam> page = new PageParam<>(param);
        SysRole role = SecurityUtil.getRole();
        SysAccount account = SecurityUtil.getAccount();
        if (Constants.ROLE_SCOPE_COMPANY.equals(role.getRoleScope())) {
            param.setDeptName(account.getDeptName());
        } else if(Constants.ROLE_SCOPE_USER.equals(role.getRoleScope())) {
            param.setCreator(account.getUsername());
        }
        page.setDefaultOrder("inupttime desc");
        return success(pjOSendformService.page(page, page.getWrapper()));
    }
    
    /**
     * 查询送评单列表（不分页）
     */
    @PreAuthorize("hasAuthority('banknote:sendform:list')")
    @OperationLog
    @GetMapping("/list")
    public List<PjOSendform> list(PjOSendformParam param) {
        PageParam<PjOSendform, PjOSendformParam> page = new PageParam<>(param);
        SysRole role = SecurityUtil.getRole();
        SysAccount account = SecurityUtil.getAccount();
        if (Constants.ROLE_SCOPE_COMPANY.equals(role.getRoleScope())) {
            param.setDeptName(account.getDeptName());
        } else if(Constants.ROLE_SCOPE_USER.equals(role.getRoleScope())) {
            param.setCreator(account.getUsername());
        }
        page.setDefaultOrder("inupttime desc");
        return pjOSendformService.list(page.getWrapper());
    }

    /**
     * 根据id查询送评单
     */
    @PreAuthorize("hasAuthority('banknote:sendform:list')")
    @OperationLog
    @GetMapping("/info/{id}")
    public ApiResult<PjOSendform> info(@PathVariable("id") String id) {
        return success(pjOSendformService.getById(id));
    }

    /**
     * 获取送评单详情（包含钱币明细）
     */
    @PreAuthorize("hasAuthority('banknote:sendform:list')")
    @OperationLog
    @GetMapping("/detail/{sendnum}")
    public ApiResult<?> getDetail(@PathVariable("sendnum") String sendnum) {
        // 获取送评单基本信息
        PjOSendform sendform = pjOSendformService.lambdaQuery()
                .eq(PjOSendform::getSendnum, sendnum)
                .one();

        if (sendform == null) {
            return fail("送评单不存在");
        }

        // 获取钱币明细
        List<PjOSendformItem> items = pjOSendformItemService.listBySendnum(sendnum);

        // 按钱币类型分组
        Map<String, List<PjOSendformItem>> groupedItems = items.stream()
                .collect(Collectors.groupingBy(item ->
                        item.getCoinType() != null ? item.getCoinType() : "其他"));

        SendformDetailDto detailDto = new SendformDetailDto();
        detailDto.setSendform(sendform);
        detailDto.setItems(items);
        // 使用动态码表获取钱币类型名称，替代硬编码
        String ancientCoinType = CodeCommonUtil.getCoinTypeNameByCode("ancientCoin"); // 古钱币
        String machineCoinType = CodeCommonUtil.getCoinTypeNameByCode("machineCoin"); // 机制币  
        String silverIngotType = CodeCommonUtil.getCoinTypeNameByCode("silverIngot"); // 银锭
        String banknoteType = CodeCommonUtil.getCoinTypeNameByCode("banknote");       // 纸币
        
        detailDto.setAncientCoins(groupedItems.getOrDefault(ancientCoinType, new ArrayList<>()));
        detailDto.setMachineCoins(groupedItems.getOrDefault(machineCoinType, new ArrayList<>()));
        detailDto.setSilverIngots(groupedItems.getOrDefault(silverIngotType, new ArrayList<>()));
        detailDto.setBanknotes(groupedItems.getOrDefault(banknoteType, new ArrayList<>()));

        return success(detailDto);
    }

    /**
     * 根据送评条码获取钱币详情页面（用于二维码扫码预览）
     * 无需权限验证，供外部扫码访问
     * 公开接口，任何人都可以访问
     */
    @GetMapping("/preview/{diyCode}")
    public ApiResult<?> getCoinPreviewByDiyCode(@PathVariable("diyCode") String diyCode) {
        try {
            if (!StringUtils.hasText(diyCode)) {
                return fail("送评条码不能为空");
            }

            // 根据送评条码查询钱币信息
            LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PjOSendformItem::getDiyCode, diyCode.trim());
            PjOSendformItem coin = pjOSendformItemService.getOne(wrapper);

            if (coin == null) {
                return fail("未找到对应的钱币信息");
            }

            // 获取送评单基本信息
            PjOSendform sendform = pjOSendformService.lambdaQuery()
                    .eq(PjOSendform::getSendnum, coin.getSendnum())
                    .one();

            // 构建预览数据
            Map<String, Object> previewData = new HashMap<>();
            previewData.put("coin", coin);
            previewData.put("sendform", sendform);

            // 添加格式化后的显示数据
            Map<String, Object> displayData = new HashMap<>();
            displayData.put("coinName", getCombinedCoinName(coin));
            displayData.put("serialNumberWithVersion", formatSerialNumberWithVersion(coin));
            displayData.put("customerName", sendform != null ? sendform.getRname() : "");
            displayData.put("companyName", sendform != null ? sendform.getDeptName() : "");
            displayData.put("submitDate", sendform != null ? sendform.getInupttime() : null);

            previewData.put("display", displayData);

            return success(previewData);
        } catch (Exception e) {
            log.error("获取钱币预览详情失败", e);
            return fail("获取钱币详情失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除送评单
     */
    @PreAuthorize("hasAuthority('banknote:sendform:remove')")
    @OperationLog
    @PostMapping("/remove")
    public ApiResult<?> remove(@RequestBody List<String> ids) {
        if (pjOSendformService.removeAndItemsByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }
    
    /**
     * 添加或修改送评单
     */
    @PreAuthorize("hasAuthority('banknote:sendform:saveOrUpdate')")
    @OperationLog
    @PostMapping("/saveOrUpdate")
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<?> saveOrUpdate(@RequestBody SendformCreateDto dto) {
        try {
            PjOSendform sendform = dto.getSendform();
            boolean isUpdate = sendform.getId() != null;
            String sendnum;
            SysAccount account = SecurityUtil.getAccount();
            sendform.setCreator(account.getUsername());
            sendform.setDeptName(account.getDeptName());
            if (isUpdate) {
                // 修改操作
                sendnum = sendform.getSendnum();
                if (sendnum == null || sendnum.trim().isEmpty()) {
                    return fail("送评单号不能为空");
                }
                
                // 设置更新时间
                if (sendform.getUpdatetime() == null) {
                    sendform.setUpdatetime(LocalDateTime.now());
                }
                
                boolean updateResult = pjOSendformService.updateById(sendform);
                if (!updateResult) {
                    return fail("更新送评单失败");
                }
            } else {
                // 新增操作
                sendnum = generateSendnum();
                sendform.setSendnum(sendnum);
                
                // 设置创建时间
                if (sendform.getInupttime() == null) {
                    sendform.setInupttime(LocalDateTime.now());
                }
                
                // 设置其他默认值
                if (sendform.getCheckStatus() == null) {
                    sendform.setCheckStatus(0); // 默认未审核
                }
                if (sendform.getIfyou() == null) {
                    sendform.setIfyou(0); // 默认未核对
                }
                if (sendform.getFullyOpen() == null) {
                    sendform.setFullyOpen(0); // 默认未开启
                }
                
                boolean saveResult = pjOSendformService.save(sendform);
                if (!saveResult) {
                    return fail("保存送评单失败");
                }
            }
            
            List<PjOSendformItem> allItems = new ArrayList<>();
            
            // 使用动态码表获取钱币类型名称，替代硬编码
            String ancientCoinType = CodeCommonUtil.getCoinTypeNameByCode("ancientCoin"); // 古钱币
            String machineCoinType = CodeCommonUtil.getCoinTypeNameByCode("machineCoin"); // 机制币  
            String silverIngotType = CodeCommonUtil.getCoinTypeNameByCode("silverIngot"); // 银锭
            String banknoteType = CodeCommonUtil.getCoinTypeNameByCode("banknote");       // 纸币
            
            if (dto.getAncientCoins() != null && !dto.getAncientCoins().isEmpty()) {
                dto.getAncientCoins().forEach(item -> {
                    item.setCoinType(ancientCoinType);
                    allItems.add(item);
                });
            }
            
            if (dto.getMachineCoins() != null && !dto.getMachineCoins().isEmpty()) {
                dto.getMachineCoins().forEach(item -> {
                    item.setCoinType(machineCoinType);
                    allItems.add(item);
                });
            }
            
            if (dto.getSilverIngots() != null && !dto.getSilverIngots().isEmpty()) {
                dto.getSilverIngots().forEach(item -> {
                    item.setCoinType(silverIngotType);
                    allItems.add(item);
                });
            }
            
            if (dto.getBanknotes() != null && !dto.getBanknotes().isEmpty()) {
                dto.getBanknotes().forEach(item -> {
                    item.setCoinType(banknoteType);
                    allItems.add(item);
                });
            }
            
            // 批量保存钱币明细
            if (!allItems.isEmpty()) {
                try {
                    pjOSendformItemService.saveBatchItems(sendnum, allItems);
                } catch (Exception itemException) {
                    itemException.printStackTrace();
                    throw itemException;
                }
            }
            
            return success(isUpdate ? "更新送评单成功" : "创建送评单成功", sendnum);
        } catch (Exception e) {
            e.printStackTrace();
            return fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 批量审核送评单
     */
    @PreAuthorize("hasAuthority('banknote:sendform:check')")
    @OperationLog
    @PostMapping("/batchCheck")
    public ApiResult<?> batchCheck(@RequestBody SendformCheckDto checkDto) {
        try {
            // 验证参数
            if (checkDto.getSendnums() == null || checkDto.getSendnums().isEmpty()) {
                return fail("送评单号列表不能为空");
            }

            if (checkDto.getCheckStatus() == null) {
                return fail("审核状态不能为空");
            }

            CheckStatusEnum checkStatus = CheckStatusEnum.getByCode(checkDto.getCheckStatus());
            if (checkStatus == null) {
                return fail("无效的审核状态");
            }

            // 设置审核人
            SysAccount account = SecurityUtil.getAccount();
            checkDto.setChecker(account.getUsername());

            // 执行审核
            boolean result = pjOSendformService.batchCheck(checkDto);
            if (result) {
                return success("审核完成");
            } else {
                return fail("审核失败");
            }
        } catch (Exception e) {
            return fail("审核失败：" + e.getMessage());
        }
    }

    /**
     * 检查送评单钱币打分情况（POST方式）
     */
    @PreAuthorize("hasAuthority('banknote:sendform:check')")
    @OperationLog
    @PostMapping("/checkUnScoredCoins")
    public ApiResult<?> checkUnScoredCoins(@RequestBody List<String> sendnums) {
        try {
            if (sendnums == null || sendnums.isEmpty()) {
                return fail("送评单号列表不能为空");
            }

            Map<String, List<PjOSendformItem>> unScoredCoins = pjOSendformService.batchCheckUnScoredCoins(sendnums);

            CoinScoreCheckResult result;
            if (unScoredCoins.isEmpty()) {
                result = CoinScoreCheckResult.success();
                return success(result.getMessage(), result);
            } else {
                result = CoinScoreCheckResult.failure(unScoredCoins);
                return success("检查完成", result);
            }
        } catch (Exception e) {
            return fail("检查失败：" + e.getMessage());
        }
    }

    /**
     * 检查单个送评单钱币打分情况（GET方式）
     */
    @PreAuthorize("hasAuthority('banknote:sendform:check')")
    @OperationLog
    @GetMapping("/checkUnScoredCoins/{sendnum}")
    public ApiResult<?> checkSingleSendformCoins(@PathVariable("sendnum") String sendnum) {
        try {
            if (!StringUtils.hasText(sendnum)) {
                return fail("送评单号不能为空");
            }

            List<PjOSendformItem> unScoredCoins = pjOSendformService.checkUnScoredCoins(sendnum);

            CoinScoreCheckResult result;
            if (unScoredCoins.isEmpty()) {
                result = CoinScoreCheckResult.success();
                return success(result.getMessage(), result);
            } else {
                Map<String, List<PjOSendformItem>> unScoredMap = new HashMap<>();
                unScoredMap.put(sendnum, unScoredCoins);
                result = CoinScoreCheckResult.failure(unScoredMap);
                return success("检查完成", result);
            }
        } catch (Exception e) {
            return fail("检查失败：" + e.getMessage());
        }
    }

    /**
     * 审核通过
     */
    @PreAuthorize("hasAuthority('banknote:sendform:check')")
    @OperationLog
    @PostMapping("/checkYesBatch")
    public ApiResult<?> checkYesBatch(@RequestBody List<String> sendnums) {
        try {
            if (sendnums == null || sendnums.isEmpty()) {
                return fail("送评单号列表不能为空");
            }

            SysAccount account = SecurityUtil.getAccount();
            String checker = account.getUsername();

            // 验证送评单是否存在
            for (String sendnum : sendnums) {
                PjOSendform sendform = pjOSendformService.lambdaQuery()
                        .eq(PjOSendform::getSendnum, sendnum)
                        .one();
                if (sendform == null) {
                    return fail("送评单不存在: " + sendnum);
                }
            }

            boolean result = pjOSendformService.approveByBatch(sendnums, checker, "审核通过");
            if (result) {
                return success("审核通过成功");
            } else {
                return fail("审核通过失败");
            }
        } catch (Exception e) {
            log.error("批量审核通过异常", e);
            return fail("审核通过失败：" + e.getMessage());
        }
    }

    /**
     * 审核驳回
     */
    @PreAuthorize("hasAuthority('banknote:sendform:check')")
    @OperationLog
    @PostMapping("/checkNoBatch")
    public ApiResult<?> checkNoBatch(@RequestBody SendformCheckDto checkDto) {
        try {
            if (checkDto.getSendnums() == null || checkDto.getSendnums().isEmpty()) {
                return fail("送评单号列表不能为空");
            }

            if (!CheckStatusEnum.REJECTED.getCode().equals(checkDto.getCheckStatus())) {
                checkDto.setCheckStatus(CheckStatusEnum.REJECTED.getCode());
            }

            SysAccount account = SecurityUtil.getAccount();
            checkDto.setChecker(account.getUsername());

            // 驳回必须有原因
            if (checkDto.getCheckRemark() == null || checkDto.getCheckRemark().trim().isEmpty()) {
                return fail("驳回原因不能为空");
            }

            boolean result = pjOSendformService.batchCheck(checkDto);
            if (result) {
                return success("审核驳回成功");
            } else {
                return fail("审核驳回失败");
            }
        } catch (Exception e) {
            return fail("审核驳回失败：" + e.getMessage());
        }
    }


    /**
     * 为送评单增加钱币
     */
    @PreAuthorize("hasAuthority('banknote:sendform:addCoin')")
    @OperationLog
    @PostMapping("/addCoins/{sendnum}")
    public ApiResult<?> addCoins(@PathVariable("sendnum") String sendnum, 
                                @RequestBody List<PjOSendformItem> items) {
        try {
            // 验证送评单是否存在
            PjOSendform sendform = pjOSendformService.lambdaQuery()
                    .eq(PjOSendform::getSendnum, sendnum)
                    .one();
            
            if (sendform == null) {
                return fail("送评单不存在");
            }
            
            // 获取当前最大序号
            List<PjOSendformItem> existingItems = pjOSendformItemService.listBySendnum(sendnum);
            int maxSeqno = existingItems.stream()
                    .mapToInt(item -> item.getSeqno() != null ? item.getSeqno() : 0)
                    .max()
                    .orElse(0);
            
            // 设置新钱币的信息
            for (int i = 0; i < items.size(); i++) {
                PjOSendformItem item = items.get(i);
                item.setSendnum(sendnum);
                item.setSeqno(maxSeqno + i + 1);
                
                // 设置创建时间（如果为空）
                if (item.getCreateTime() == null) {
                    item.setCreateTime(new java.util.Date());
                }
                
                // 确保其他必要字段不为空
                if (item.getGradeFee() == null) {
                    item.setGradeFee(new java.math.BigDecimal("0.00"));
                }
            }
            
            // 保存新钱币
            if (pjOSendformItemService.saveBatch(items)) {
                return success("添加钱币成功");
            } else {
                return fail("添加钱币失败");
            }
        } catch (Exception e) {
            return fail("添加钱币失败：" + e.getMessage());
        }
    }

    /**
     * 更新单个钱币信息
     */
    @PreAuthorize("hasAuthority('banknote:sendform:updateCoin')")
    @OperationLog
    @PutMapping("/updateCoin")
    public ApiResult<?> updateCoin(@RequestBody PjOSendformItem item) {
        if (pjOSendformItemService.updateById(item)) {
            return success("更新钱币信息成功");
        }
        return fail("更新钱币信息失败");
    }

    /**
     * 删除钱币
     */
    @PreAuthorize("hasAuthority('banknote:sendform:removeCoin')")
    @OperationLog
    @DeleteMapping("/removeCoin/{id}")
    public ApiResult<?> removeCoin(@PathVariable("id") String id) {
        if (pjOSendformItemService.removeById(id)) {
            return success("删除钱币成功");
        }
        return fail("删除钱币失败");
    }

    /**
     * 获取钱币类型枚举
     */
    @GetMapping("/coinTypes")
    public ApiResult<List<Map<String, Object>>> getCoinTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        for (CoinTypeEnum type : CoinTypeEnum.values()) {
            Map<String, Object> map = Map.of(
                "code", type.getCode(),
                "name", type.getName()
            );
            types.add(map);
        }
        return success(types);
    }

    /**
     * 获取审核状态枚举
     */
    @GetMapping("/checkStatuses")
    public ApiResult<List<Map<String, Object>>> getCheckStatuses() {
        List<Map<String, Object>> statuses = new ArrayList<>();
        for (CheckStatusEnum status : CheckStatusEnum.values()) {
            Map<String, Object> map = Map.of(
                "code", status.getCode(),
                "name", status.getName()
            );
            statuses.add(map);
        }
        return success(statuses);
    }

    /**
     * 开启扫码审核
     */
    @PreAuthorize("hasAuthority('banknote:sendform:scanAudit')")
    @OperationLog
    @PostMapping("/enableScanAudit")
    public ApiResult<?> enableScanAudit(@RequestBody List<String> sendnums) {
        try {
            if (sendnums == null || sendnums.isEmpty()) {
                return fail("送评单号列表不能为空");
            }

            boolean result = pjOSendformService.enableScanAudit(sendnums);
            if (result) {
                return success("扫码审核开启成功");
            } else {
                return fail("扫码审核开启失败");
            }
        } catch (Exception e) {
            return fail("开启扫码审核失败：" + e.getMessage());
        }
    }

    /**
     * 关闭扫码审核
     */
    @PreAuthorize("hasAuthority('banknote:sendform:scanAudit')")
    @OperationLog
    @PostMapping("/disableScanAudit")
    public ApiResult<?> disableScanAudit(@RequestBody List<String> sendnums) {
        try {
            if (sendnums == null || sendnums.isEmpty()) {
                return fail("送评单号列表不能为空");
            }

            boolean result = pjOSendformService.disableScanAudit(sendnums);
            if (result) {
                return success("扫码审核关闭成功");
            } else {
                return fail("扫码审核关闭失败");
            }
        } catch (Exception e) {
            return fail("关闭扫码审核失败：" + e.getMessage());
        }
    }

    /**
     * 自动开启扫码审核（满足条件时）
     */
    @PreAuthorize("hasAuthority('banknote:sendform:scanAudit')")
    @OperationLog
    @PostMapping("/autoEnableScanAudit")
    public ApiResult<?> autoEnableScanAudit(@RequestBody List<String> sendnums) {
        try {
            if (sendnums == null || sendnums.isEmpty()) {
                return fail("送评单号列表不能为空");
            }

            Map<String, Object> result = pjOSendformService.autoEnableScanAudit(sendnums);
            return success(result);
        } catch (Exception e) {
            return fail("自动开启扫码审核失败：" + e.getMessage());
        }
    }

    /**
     * 检查送评单是否满足自动开启扫码审核的条件
     */
    @PreAuthorize("hasAuthority('banknote:sendform:scanAudit')")
    @PostMapping("/checkAutoEnableConditions")
    public ApiResult<?> checkAutoEnableConditions(@RequestBody List<String> sendnums) {
        try {
            if (sendnums == null || sendnums.isEmpty()) {
                return fail("送评单号列表不能为空");
            }

            Map<String, Object> result = pjOSendformService.checkAutoEnableConditions(sendnums);
            return success(result);
        } catch (Exception e) {
            return fail("检查自动开启条件失败：" + e.getMessage());
        }
    }



    /**
     * 生成送评单号
     * 格式：00792 + yMMdd + 序号 (11位)
     * 示例：*********** (00792 + 5年3月02日 + 57号)
     */
    private synchronized String generateSendnum() {
        String prefix = "00792";
        LocalDateTime now = LocalDateTime.now();

        // 年份取最后一位
        String year = String.valueOf(now.getYear() % 10);

        // 月日格式化为MMdd
        String monthDay = now.format(DateTimeFormatter.ofPattern("MMdd"));

        // 查询当天的最大序号
        String datePrefix = prefix + year + monthDay;
        Integer maxSeq = getMaxSendnumSequence(datePrefix);
        if (maxSeq == null) {
            maxSeq = 0;
        }

        // 生成下一个序号，2位格式
        int nextSeq = maxSeq + 1;
        String seqStr = String.format("%02d", nextSeq);

        return datePrefix + seqStr;
    }

    /**
     * 查询指定日期前缀的最大送评单序号
     */
    private Integer getMaxSendnumSequence(String datePrefix) {
        return pjOSendformMapper.getMaxSendnumSequence(datePrefix);
    }

    /**
     * 获取组合钱币名称
     */
    private String getCombinedCoinName(PjOSendformItem coin) {
        StringBuilder coinName = new StringBuilder();

        if (StringUtils.hasText(coin.getCoinName1())) {
            coinName.append(coin.getCoinName1());
        }
        if (StringUtils.hasText(coin.getCoinName2())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(coin.getCoinName2());
        }
        if (StringUtils.hasText(coin.getCoinName3())) {
            if (coinName.length() > 0) coinName.append(" ");
            coinName.append(coin.getCoinName3());
        }

        return coinName.toString();
    }

    /**
     * 格式化序列号和版本的组合
     */
    private String formatSerialNumberWithVersion(PjOSendformItem coin) {
        String serialNumber = coin.getSerialNumber();
        String version = coin.getVersion();

        if (serialNumber != null && !serialNumber.trim().isEmpty() &&
            version != null && !version.trim().isEmpty()) {
            return serialNumber + " - " + version;
        } else if (serialNumber != null && !serialNumber.trim().isEmpty()) {
            return serialNumber;
        } else if (version != null && !version.trim().isEmpty()) {
            return version;
        }
        return "";
    }
}