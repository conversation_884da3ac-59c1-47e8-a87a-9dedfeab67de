# 开发环境配置

# 应用配置
app:
  preview:
    base-url: http://localhost:7070

# 数据源配置
spring:
  datasource:
    url: ****************************************
    username: dev_platform
    password: dev_platform
    driver-class-name: oracle.jdbc.OracleDriver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      connection-properties: oracle.jdbc.timezoneAsRegion=true
      filters: stat,log4j
      validation-query: SELECT 1 FROM DUAL
  data:
    mongodb:
      host: paynexc.home
      port: 27017
      authentication-database: admin
      database: server
      username: mongo_txRpa7
      password: "mongo_JdKRtZ"
      auto-index-creation: true
      uuid-representation: JAVA_LEGACY  # UUID表示方式：JAVA_LEGACY, C_SHARP_LEGACY, PYTHON_LEGACY, STANDARD
      gridfs:
        bucket: fs
        chunk-size-bytes: 261120
      ssl:
        enabled: false                  # 是否启用 SSL
    redis:
      host: paynexc.home
      port: 6379
      password: redis_DGzpaM
      timeout: 10000
      database: 1

# 日志配置
logging:
  level:
    com.payne: debug
    com.baomidou.mybatisplus: debug

payne:
  license:
    enabled: false