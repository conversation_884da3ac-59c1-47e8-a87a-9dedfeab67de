# 生产环境配置
app:
  preview:
    base-url: https://your-domain.com  # 生产环境请修改为实际域名

spring:
  datasource:
    url: ******************************************
    username: dev_platform
    password: dev_platform_20250714
    driver-class-name: oracle.jdbc.OracleDriver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      connection-properties: oracle.jdbc.timezoneAsRegion=true
      filters: stat,log4j
      validation-query: SELECT 1 FROM DUAL
  data:
    mongodb:
      host: **************
      port: 27017
      authentication-database: admin
      database: server
      username: mongo_NZtFXx
      password: "mongo_4PJwyF"
      auto-index-creation: true
      uuid-representation: JAVA_LEGACY  # UUID表示方式：JAVA_LEGACY, C_SHARP_LEGACY, PYTHON_LEGACY, STANDARD
      gridfs:
        bucket: fs
        chunk-size-bytes: 261120
      ssl:
        enabled: false                  # 是否启用 SSL
    redis:
      host: **************
      port: 6379
      password: redis_JnbF6e
      timeout: 10000
      database: 1
  # Redisson 配置
  redisson:
    config: |
      singleServerConfig:
        address: "redis://**************:6379"
        password: "redis_JnbF6e"
        database: 1
        connectionMinimumIdleSize: 8
        connectionPoolSize: 32
        dnsMonitoringInterval: 5000
        subscriptionConnectionMinimumIdleSize: 1
        subscriptionConnectionPoolSize: 50
        subscriptionConnectionTimeout: 10000
        timeout: 10000
        retryAttempts: 3
        retryInterval: 1500

logging:
  level:
    com.payne: info
    com.baomidou.mybatisplus: info
