[2m2025-08-02 00:01:51.962[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-02 00:01:51.993[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-02 00:01:51.995[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-02 00:01:52.002[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-02 00:01:52.003[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-08-02 00:01:52.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.018[0;39m [33m WARN[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 解析字段映射失败: argument "content" is null
[2m2025-08-02 00:01:52.019[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:01:52.032[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:01:52.033[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:01:52.034[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:01:52.034[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-08-02 00:01:52.057[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 110de26c9970764fffbd6af7011d2768 处理模板字段替换完成
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 247550797bd7aa10784121f51394f84b 处理模板字段替换完成
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 5b26701afbfb65c0a8a833f45581d752 处理模板字段替换完成
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 811424d68b9aea59e781950a5a47c736 处理模板字段替换完成
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 f3de6989e3d445c00fa9c159a6dbdba0 处理模板字段替换完成
[2m2025-08-02 00:01:52.069[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.083[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.085[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.085[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.086[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:01:52.092[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.093[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.110[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.112[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.112[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.112[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:01:52.119[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.120[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.135[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.137[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.137[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.137[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:01:52.144[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.144[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.157[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.159[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.160[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.160[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:01:52.167[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.168[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.183[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.185[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.185[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.185[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:01:52.192[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:05.418[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-02 00:08:05.434[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-02 00:08:05.436[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-02 00:08:05.449[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-02 00:08:05.450[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-02 00:08:05.465[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:08.110[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-02 00:08:08.418[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-02 00:08:08.420[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-02 00:08:08.446[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-02 00:08:08.448[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 00:08:08.472[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:08.487[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-02 00:08:08.487[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-02 00:08:08.499[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:11.696[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-02 00:08:11.705[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-02 00:08:11.707[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-02 00:08:11.707[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-02 00:08:11.708[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-02 00:08:11.721[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:12.791[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-08-02 00:08:12.805[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-08-02 00:08:12.807[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-08-02 00:08:12.825[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL)
[2m2025-08-02 00:08:12.826[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:12.831[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:12.831[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-02 00:08:12.831[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: ************(String), 20(Long), 0(Long)
[2m2025-08-02 00:08:12.839[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-02 00:08:12.848[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.862[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.863[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.864[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.864[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:12.871[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:12.872[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.885[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.886[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.887[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.887[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:12.893[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:12.894[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.906[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.908[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.908[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.909[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:12.916[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:12.917[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.929[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.931[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.931[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.931[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:12.939[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:12.940[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.952[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:12.954[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.954[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:12.954[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:12.963[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:17.910[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:08:17.928[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:08:17.930[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:08:17.931[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:08:17.931[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-08-02 00:08:17.938[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-02 00:08:17.941[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM IN (?))
[2m2025-08-02 00:08:17.957[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM IN (?))
[2m2025-08-02 00:08:17.959[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM IN (?))
[2m2025-08-02 00:08:17.960[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM IN (?))
[2m2025-08-02 00:08:17.960[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:17.966[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:19.953[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-02 00:08:19.964[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-02 00:08:19.966[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-02 00:08:19.967[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-02 00:08:19.967[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-08-02 00:08:19.978[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:19.980[0;39m [33m WARN[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 解析字段映射失败: argument "content" is null
[2m2025-08-02 00:08:19.981[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:08:19.996[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:08:19.999[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:08:19.999[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:08:19.999[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-08-02 00:08:20.009[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-02 00:08:20.011[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.011[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:08:20.011[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:08:20.011[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:08:20.011[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:08:20.011[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.011[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 110de26c9970764fffbd6af7011d2768 处理模板字段替换完成
[2m2025-08-02 00:08:20.012[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.012[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:08:20.012[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:08:20.012[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:08:20.012[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:08:20.012[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.012[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 247550797bd7aa10784121f51394f84b 处理模板字段替换完成
[2m2025-08-02 00:08:20.013[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.013[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:08:20.013[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:08:20.013[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:08:20.014[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:08:20.014[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.014[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 5b26701afbfb65c0a8a833f45581d752 处理模板字段替换完成
[2m2025-08-02 00:08:20.014[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.014[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 811424d68b9aea59e781950a5a47c736 处理模板字段替换完成
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:08:20.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 f3de6989e3d445c00fa9c159a6dbdba0 处理模板字段替换完成
[2m2025-08-02 00:08:20.016[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.032[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.034[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.034[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.034[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:20.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:20.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.056[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.058[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.058[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.059[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:20.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:20.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.079[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.081[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.081[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.081[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:20.088[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:20.089[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.100[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.101[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.101[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.102[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:20.108[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:08:20.109[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.121[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:08:20.123[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.123[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:08:20.123[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:08:20.130[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:14:54.942[0;39m [33m WARN[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.payne.**.mapper]' package. Please check your configuration.
[2m2025-08-02 00:14:54.943[0;39m [32m INFO[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-02 00:14:54.952[0;39m [32m INFO[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 JPA repository interfaces.
[2m2025-08-02 00:14:54.963[0;39m [32m INFO[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-02 00:14:54.966[0;39m [32m INFO[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
[2m2025-08-02 00:14:54.966[0;39m [32m INFO[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-02 00:14:54.969[0;39m [32m INFO[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 0 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-02 00:14:55.147[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,   PAGE_SETTINGS=?, IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-08-02 00:14:55.153[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,   PAGE_SETTINGS=?, IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-08-02 00:14:55.155[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, PAGE_SETTINGS = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-08-02 00:14:55.155[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, PAGE_SETTINGS = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-08-02 00:14:55.156[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":369,"top":1.5,"height":42,"width":75,"title":"品相打分","right":444,"bottom":43.5,"vCenter":406.5,"hCenter":22.5,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{gradeScore}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":465,"top":1.5,"height":58.5,"width":66,"title":"二维码","qrcodeType":"qrcode","right":525.75,"bottom":53.25,"vCenter":495,"hCenter":27.75,"qrCodeLevel":0,"coordinateSync":false,"widthHeightSync":false,"field":"{qrcode}","testData":"qrcode"},"printElementType":{"title":"二维码","type":"qrcode"}},{"options":{"left":114,"top":6,"height":9.75,"width":141,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":254.25,"bottom":15,"vCenter":183.75,"hCenter":10.125,"field":"{bankName}","fontFamily":"SimSun","fontSize":12,"hideTitle":true},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":450,"top":12,"height":40.5,"width":9,"title":"特殊标记","right":458.25,"bottom":52.5,"vCenter":453.75,"hCenter":32.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{specialMark}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":27,"height":9.75,"width":139.5,"title":"钱币名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{coinName1}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":369,"top":49.5,"height":12,"width":75,"title":"品相","right":439.********,"bottom":61.********,"vCenter":405.********,"hCenter":55.********,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{gradeScore}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":49.5,"height":9.75,"width":139.5,"title":"钱币编号-版别","right":234.75,"bottom":59.25,"vCenter":174.75,"hCenter":54.375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{serialNumber}-{version}"},"printElementType":{"title":"文本","type":"text"}}],"paperNumberLeft":388.5,"paperNumberTop":51,"paperNumberDisabled":true,"paperNumberContinue":true,"watermarkOptions":{"content":"","fillStyle":"rgba(184, 184, 184, 0.3)","fontSize":"14px","rotate":25,"width":200,"height":200,"timestamp":false,"format":"YYYY-MM-DD HH:mm"},"panelLayoutOptions":{"layoutType":"column","layoutRowGap":0,"layoutColumnGap":0}}]}(String), {"paperWidth":192,"paperHeight":290,"paperType":"custom","timestamp":"2025-08-01T16:14:54.916Z"}(String), false(Boolean), 2025-08-02T00:14:55.133687(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-08-02 00:14:55.177[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-02 00:14:55.222[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-02 00:14:55.239[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-02 00:14:55.243[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-02 00:14:55.243[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-02 00:14:55.243[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-02 00:14:55.253[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:14:57.992[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-02 00:14:58.001[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-02 00:14:58.003[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-02 00:14:58.003[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-02 00:14:58.003[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-08-02 00:14:58.014[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:14:58.015[0;39m [33m WARN[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 解析字段映射失败: argument "content" is null
[2m2025-08-02 00:14:58.017[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:14:58.028[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:14:58.030[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:14:58.031[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:14:58.031[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-08-02 00:14:58.038[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-02 00:14:58.039[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.039[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:14:58.039[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:14:58.040[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:14:58.040[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:14:58.040[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.041[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 110de26c9970764fffbd6af7011d2768 处理模板字段替换完成
[2m2025-08-02 00:14:58.041[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.041[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:14:58.041[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:14:58.041[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:14:58.041[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:14:58.041[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.041[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 247550797bd7aa10784121f51394f84b 处理模板字段替换完成
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 5b26701afbfb65c0a8a833f45581d752 处理模板字段替换完成
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:14:58.042[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 811424d68b9aea59e781950a5a47c736 处理模板字段替换完成
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:14:58.043[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 f3de6989e3d445c00fa9c159a6dbdba0 处理模板字段替换完成
[2m2025-08-02 00:14:58.046[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.059[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.060[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.060[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.061[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:14:58.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:14:58.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.080[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.082[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.082[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.082[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:14:58.087[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:14:58.088[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.100[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.101[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.102[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.102[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:14:58.107[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:14:58.108[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.119[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.121[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.121[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.121[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:14:58.130[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:14:58.131[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.142[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:14:58.143[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.144[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:14:58.144[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-02 00:14:58.150[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:36:25.764[0;39m [32m INFO[0;39m [35m48506[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-02 00:36:25.777[0;39m [32m INFO[0;39m [35m48506[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
