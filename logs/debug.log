[2m2025-08-02 00:01:51.962[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-02 00:01:51.993[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-08-02 00:01:51.995[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-02 00:01:52.002[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-08-02 00:01:52.003[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-08-02 00:01:52.015[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.018[0;39m [33m WARN[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 解析字段映射失败: argument "content" is null
[2m2025-08-02 00:01:52.019[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:01:52.032[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-08-02 00:01:52.033[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:01:52.034[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-08-02 00:01:52.034[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-08-02 00:01:52.057[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.065[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 110de26c9970764fffbd6af7011d2768 处理模板字段替换完成
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 247550797bd7aa10784121f51394f84b 处理模板字段替换完成
[2m2025-08-02 00:01:52.066[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 5b26701afbfb65c0a8a833f45581d752 处理模板字段替换完成
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.067[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 811424d68b9aea59e781950a5a47c736 处理模板字段替换完成
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-08-02 00:01:52.068[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 f3de6989e3d445c00fa9c159a6dbdba0 处理模板字段替换完成
[2m2025-08-02 00:01:52.069[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.083[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.085[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.085[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.086[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 007925071801(String)
[2m2025-08-02 00:01:52.092[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.093[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.110[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.112[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.112[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.112[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 007925071801(String)
[2m2025-08-02 00:01:52.119[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.120[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.135[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.137[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.137[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.137[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 007925071801(String)
[2m2025-08-02 00:01:52.144[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.144[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.157[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.159[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.160[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.160[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 007925071801(String)
[2m2025-08-02 00:01:52.167[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 00:01:52.168[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.183[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-02 00:01:52.185[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.185[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-02 00:01:52.185[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 007925071801(String)
[2m2025-08-02 00:01:52.192[0;39m [32mDEBUG[0;39m [35m48506[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
