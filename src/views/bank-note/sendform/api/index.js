import request from '@/utils/request';

/**
 * 添加或修改送评单
 */
export async function saveOrUpdateSendform(data) {
  const res = await request.post('/pjosendform/saveOrUpdate', data);
  if (res.data.code === 0) {
    return res.data.data || res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 为送评单增加钱币
 */
export async function addCoins(sendnum, data) {
  const res = await request.post(`/pjosendform/addCoins/${sendnum}`, data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 更新钱币信息
 */
export async function updateCoin(data) {
  const res = await request.put('/pjosendform/updateCoin', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除钱币
 */
export async function removeCoin(id) {
  const res = await request.delete(`/pjosendform/removeCoin/${id}`);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除送评单
 */
export async function removes(data) {
  const res = await request.post('/pjosendform/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 审核通过
 */
export async function checkYesBatch(data) {
  const res = await request.post('/pjosendform/checkYesBatch', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 审核驳回
 */
export async function checkNoBatch(data) {
  const res = await request.post('/pjosendform/checkNoBatch', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取钱币类型枚举
 */
export async function getCoinTypes() {
  const res = await request.get('/pjosendform/coinTypes');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询送评单列表
 */
export async function queryPage(params) {
  const res = await request.get('/pjosendform/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据送评单号获取详情（包含钱币明细）
 */
export async function getSendformDetail(sendnum) {
  const res = await request.get(`/pjosendform/detail/${sendnum}`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据送评条码获取钱币预览详情（用于二维码扫码）
 */
export async function getCoinPreviewByDiyCode(diyCode) {
  const res = await request.get(`/pjosendform/preview/${diyCode}`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量检查送评单钱币打分情况
 */
export async function checkUnScoredCoins(sendnums) {
  const res = await request.post('/pjosendform/checkUnScoredCoins', sendnums);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 检查单个送评单钱币打分情况
 */
export async function checkSingleSendformCoins(sendnum) {
  const res = await request.get(`/pjosendform/checkUnScoredCoins/${sendnum}`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 开启扫码审核
 */
export async function enableScanAudit(sendnums) {
  const res = await request.post('/pjosendform/enableScanAudit', sendnums);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 关闭扫码审核
 */
export async function disableScanAudit(sendnums) {
  const res = await request.post('/pjosendform/disableScanAudit', sendnums);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 自动开启扫码审核
 */
export async function autoEnableScanAudit(sendnums) {
  const res = await request.post('/pjosendform/autoEnableScanAudit', sendnums);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 检查送评单是否可以自动开启扫码审核
 */
export async function checkAutoEnableConditions(sendnums) {
  const res = await request.post('/pjosendform/checkAutoEnableConditions', sendnums);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


