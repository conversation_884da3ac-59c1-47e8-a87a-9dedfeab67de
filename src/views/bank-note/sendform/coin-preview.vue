<template>
  <div class="coin-preview-container">
    <el-card v-loading="loading" class="preview-card">
      <!-- 头部信息 -->
      <template #header>
        <div class="card-header">
          <h2>钱币详情预览</h2>
          <el-tag v-if="coinData?.coin?.diyCode" type="primary" size="large">
            {{ coinData.coin.diyCode }}
          </el-tag>
        </div>
      </template>

      <!-- 错误信息 -->
      <el-alert
        v-if="error"
        :title="error"
        type="error"
        show-icon
        :closable="false"
        class="error-alert"
      />

      <!-- 钱币详情 -->
      <div v-else-if="coinData" class="coin-details">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="钱币名称">
            {{ coinData.display?.coinName || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="钱币类型">
            {{ coinData.coin?.coinType || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="序列号-版别">
            {{ coinData.display?.serialNumberWithVersion || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="年代信息">
            {{ coinData.coin?.yearInfo || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="送评条码">
            {{ coinData.coin?.diyCode || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="评级分数">
            {{ coinData.coin?.gradeScore || '未评级' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 送评单信息 -->
        <el-descriptions title="送评单信息" :column="2" border class="mt-4">
          <el-descriptions-item label="送评单号">
            {{ coinData.coin?.sendnum || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户姓名">
            {{ coinData.display?.customerName || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="送评公司">
            {{ coinData.display?.companyName || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ formatDate(coinData.display?.submitDate) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 评级信息 -->
        <el-descriptions title="评级信息" :column="2" border class="mt-4">
          <el-descriptions-item label="真伪性">
            {{ coinData.coin?.authenticity || '未鉴定' }}
          </el-descriptions-item>
          <el-descriptions-item label="特殊标记">
            {{ coinData.coin?.specialMark || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="评分备注">
            {{ coinData.coin?.scoreRemarks || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="验货标注">
            {{ coinData.coin?.inspectionNote || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 钱币图片 -->
        <div v-if="coinImages.length > 0" class="coin-images mt-4">
          <h3>钱币图片</h3>
          <el-row :gutter="16">
            <el-col v-for="(image, index) in coinImages" :key="index" :span="6">
              <el-image
                :src="image"
                :preview-src-list="coinImages"
                :initial-index="index"
                fit="cover"
                class="coin-image"
                :preview-teleported="true"
              />
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 底部操作 -->
      <template #footer>
        <div class="card-footer">
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="refresh">刷新</el-button>
        </div>
      </template>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCoinPreviewByDiyCode } from './api'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const error = ref('')
const coinData = ref(null)

// 计算属性
const coinImages = computed(() => {
  if (!coinData.value?.coin?.coinImages) return []
  try {
    return JSON.parse(coinData.value.coin.coinImages)
  } catch (e) {
    return []
  }
})

// 方法
const loadCoinData = async () => {
  const diyCode = route.params.diyCode
  if (!diyCode) {
    error.value = '缺少送评条码参数'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const data = await getCoinPreviewByDiyCode(diyCode)
    coinData.value = data
  } catch (err) {
    error.value = err.message || '加载钱币详情失败'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

const formatDate = (date) => {
  if (!date) return '未设置'
  return new Date(date).toLocaleString('zh-CN')
}

const goBack = () => {
  router.back()
}

const refresh = () => {
  loadCoinData()
}

// 生命周期
onMounted(() => {
  loadCoinData()
})
</script>

<style scoped>
.coin-preview-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.preview-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.error-alert {
  margin-bottom: 20px;
}

.coin-details {
  padding: 20px 0;
}

.mt-4 {
  margin-top: 24px;
}

.coin-images h3 {
  margin-bottom: 16px;
  color: #303133;
}

.coin-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
