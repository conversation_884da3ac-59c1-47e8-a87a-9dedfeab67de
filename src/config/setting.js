/** 接口地址 */
export const API_BASE_URL = import.meta.env.VITE_API_URL;

/** 项目名称 */
export const PROJECT_NAME = import.meta.env.VITE_APP_NAME;

/** 不需要登录的路由 */
export const WHITE_LIST = ['/login', '/forget', '/coin-preview'];

/** 首页路径, 为空则取第一个菜单的地址 */
export const HOME_PATH = void 0;

/** 外层布局的路由地址 */
export const LAYOUT_PATH = '/';

/** 刷新路由的路由地址 */
export const REDIRECT_PATH = '/redirect';

/** 开启页签栏后是否缓存组件 */
// export const TAB_KEEP_ALIVE = !import.meta.env.DEV;
export const TAB_KEEP_ALIVE = false;

/** token本地缓存的名称 */
export const TOKEN_CACHE_NAME = 'token';

/** 当前角色本地缓存的名称 */
export const CURRENT_ROLE_CACHE_NAME = 'current-role';

/** 主题配置本地缓存的名称 */
export const THEME_CACHE_NAME = 'theme';

/** i18n本地缓存的名称 */
export const I18N_CACHE_NAME = 'i18n-lang';
// 高德地图key更换
// key：8059e13ecaa171a67b29c594d7f39b5a
// 安全密钥：329977d48d064c5483498a44d3618d98
/** 高德地图key, 请到高德地图官网自行申请 */
export const MAP_KEY = '8059e13ecaa171a67b29c594d7f39b5a';

/** EleAdminPlus授权码 */
export const LICENSE_CODE = import.meta.env.VITE_LICENSE;

/** 导入错误excel下载路径*/
export const FILE_IMPORTERROREXCEL = 'api/file/importErrorExcel';
